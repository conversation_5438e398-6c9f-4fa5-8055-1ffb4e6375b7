//@version=5
indicator("Delta Flip - Different Logic", shorttitle="DeltaFlip", overlay=true)

// ===== Core Parameters =====
windowSize = input.int(20, "Window Size (bars)", minval=1, group="Core")
sdLookback = input.int(100, "SD Lookback (bars)", minval=2, group="Core")

// ===== Gate Parameters =====
thresholdSd = input.float(0.03, "Threshold (× SD basis)", minval=0, group="Gate")
useWindowSDBasis = input.bool(true, "Use Window SD Basis", group="Gate")
usePriorStdForGate = input.bool(true, "Use Prior-Bar SD", group="Gate")
gateSdScale = input.float(0.05, "Gate SD Scale", minval=0.000001, group="Gate")
minAbs = input.float(0.0, "Min Abs (0=off)", minval=0, group="Gate")

// ===== Firing Behaviour =====
fireOnPass = input.bool(true, "Fire Immediately On Pass", group="Fire")
confirmWithinBars = input.int(0, "Confirm Within Bars (0=unlimited)", minval=0, group="Fire")
minFlipSeparation = input.int(0, "Min Flip Separation (bars)", minval=0, group="Fire")
requireSameSideHold = input.bool(false, "Require Same-Side Hold", group="Fire")
holdEpsilon = input.float(0.0, "Hold Epsilon", minval=0, group="Fire")

// ===== Visuals =====
drawRawCrossArrows = input.bool(true, "Draw Raw Zero-Cross Arrows", group="Visuals")
drawGatedFlipArrows = input.bool(true, "Draw Gated Flip Arrows", group="Visuals")
markerRadiusTicks = input.int(10, "Marker Radius (ticks)", minval=1, group="Visuals")
debugPaintDots = input.bool(true, "DEBUG: Paint dots", group="Debug")

// ===== Variables =====
var float tapeCum = 0.0
var float lastTradePrice = na
var int lastFlipBar = -999999
var int armDir = 0  // +1 up, -1 down
var int armBar = -1
var float armMaxMag = 0.0

// Arrays for storing historical data
var array<float> perBarArray = array.new<float>()
var array<float> winSumArray = array.new<float>()

// ===== Functions =====

// Calculate standard deviation manually since we need custom lookback
calcStdDev(src, length) =>
    if bar_index < length - 1
        na
    else
        sum = 0.0
        sumSq = 0.0
        for i = 0 to length - 1
            val = src[i]
            sum := sum + val
            sumSq := sumSq + val * val
        mean = sum / length
        variance = (sumSq / length) - (mean * mean)
        math.sqrt(math.max(0, variance))

// Calculate SMA manually for consistency
calcSMA(src, length) =>
    if bar_index < length - 1
        na
    else
        sum = 0.0
        for i = 0 to length - 1
            sum := sum + src[i]
        sum / length

// ===== Main Logic =====

// Calculate per-bar delta (simplified for TradingView - using volume and price direction)
perBarDelta = 0.0
if not na(close[1])
    if close > close[1]
        perBarDelta := volume
    else if close < close[1]
        perBarDelta := -volume
    else
        perBarDelta := 0

// Store per-bar delta in array (maintain only what we need)
if array.size(perBarArray) >= math.max(windowSize, sdLookback)
    array.shift(perBarArray)
array.push(perBarArray, perBarDelta)

// Calculate rolling window sum
winSum = 0.0
lookbackBars = math.min(windowSize, array.size(perBarArray))
if lookbackBars > 0
    for i = 0 to lookbackBars - 1
        winSum := winSum + array.get(perBarArray, array.size(perBarArray) - 1 - i)

// Store window sum in array
if array.size(winSumArray) >= math.max(windowSize, sdLookback)
    array.shift(winSumArray)
array.push(winSumArray, winSum)

// Calculate window mean and standard deviation
winMean = calcSMA(winSum, windowSize)
winStd = calcStdDev(winSum, windowSize)
perBarStd = calcStdDev(perBarDelta, sdLookback)

// Detrended window sum
adj = winSum - (na(winMean) ? 0 : winMean)

// Previous adjusted value for zero-cross detection
prevAdj = na
if array.size(winSumArray) >= 2
    prevWinSum = array.get(winSumArray, array.size(winSumArray) - 2)
    prevWinMean = bar_index >= windowSize ? calcSMA(prevWinSum, windowSize) : na
    prevAdj := prevWinSum - (na(prevWinMean) ? 0 : prevWinMean)

// ===== Signal Logic =====
var float flipSignal = 0

// Reset signal each bar
flipSignal := 0

// Warm-up period
warm = math.max(windowSize, sdLookback) + 2
if bar_index >= warm and not na(prevAdj)

    // Zero-cross detection
    crossUp = prevAdj <= 0 and adj >= 0
    crossDown = prevAdj >= 0 and adj <= 0

    // Draw raw cross arrows
    if drawRawCrossArrows and (crossUp or crossDown)
        if crossUp
            label.new(bar_index, low - markerRadiusTicks * syminfo.mintick, "↑",
                     style=label.style_none, textcolor=color.gray, size=size.small)
        else
            label.new(bar_index, high + markerRadiusTicks * syminfo.mintick, "↓",
                     style=label.style_none, textcolor=color.gray, size=size.small)

    // Arm the system on zero crosses
    if crossUp
        armDir := 1
        armBar := bar_index
        armMaxMag := math.abs(adj)
        if debugPaintDots
            plotchar(close, "ARM_UP", "●", location.absolute, color.yellow, size=size.tiny)

    if crossDown
        armDir := -1
        armBar := bar_index
        armMaxMag := math.abs(adj)
        if debugPaintDots
            plotchar(close, "ARM_DN", "●", location.absolute, color.yellow, size=size.tiny)

    // Same-side hold requirement
    if requireSameSideHold and armDir != 0
        if armDir > 0 and adj < -holdEpsilon
            armDir := 0
        if armDir < 0 and adj > holdEpsilon
            armDir := 0

    // Gates off - fire immediately on cross
    gatesOff = thresholdSd <= 0 and minAbs <= 0
    if gatesOff and (crossUp or crossDown)
        flipSignal := crossUp ? 1 : -1
        lastFlipBar := bar_index

        if drawGatedFlipArrows
            if flipSignal > 0
                label.new(bar_index, low - markerRadiusTicks * syminfo.mintick, "▲",
                         style=label.style_none, textcolor=color.lime, size=size.normal)
            else
                label.new(bar_index, high + markerRadiusTicks * syminfo.mintick, "▼",
                         style=label.style_none, textcolor=color.fuchsia, size=size.normal)

    // Update max magnitude if armed
    if armDir != 0
        armMaxMag := math.max(armMaxMag, math.abs(adj))

    // Gate logic
    if not gatesOff and armDir != 0
        // Get standard deviation values
        idx = usePriorStdForGate ? 1 : 0
        safeIdx = math.min(bar_index, math.max(0, idx))

        sdWinVal = na(winStd) ? 0.0 : winStd
        sdPerVal = na(perBarStd) ? 0.0 : perBarStd

        sdBasis = useWindowSDBasis ? sdWinVal : sdPerVal
        sdBasis := math.max(0.000001, sdBasis)

        thr = thresholdSd * sdBasis * math.max(0.000001, gateSdScale)

        pass = (thresholdSd > 0 and armMaxMag >= thr) or (minAbs > 0 and armMaxMag >= minAbs)

        if debugPaintDots and pass
            plotchar(close, "PASS", "●", location.absolute, color.orange, size=size.tiny)

        if pass
            if fireOnPass
                flipSignal := armDir
                lastFlipBar := bar_index
                armDir := 0

                if drawGatedFlipArrows
                    if flipSignal > 0
                        label.new(bar_index, low - markerRadiusTicks * syminfo.mintick, "▲",
                                 style=label.style_none, textcolor=color.lime, size=size.normal)
                    else
                        label.new(bar_index, high + markerRadiusTicks * syminfo.mintick, "▼",
                                 style=label.style_none, textcolor=color.fuchsia, size=size.normal)
            else
                within = confirmWithinBars <= 0 or (bar_index - armBar <= confirmWithinBars)
                sepOK = bar_index - lastFlipBar >= minFlipSeparation

                if within and sepOK
                    flipSignal := armDir
                    lastFlipBar := bar_index
                    armDir := 0

                    if drawGatedFlipArrows
                        if flipSignal > 0
                            label.new(bar_index, low - markerRadiusTicks * syminfo.mintick, "▲",
                                     style=label.style_none, textcolor=color.lime, size=size.normal)
                        else
                            label.new(bar_index, high + markerRadiusTicks * syminfo.mintick, "▼",
                                     style=label.style_none, textcolor=color.fuchsia, size=size.normal)
                else if not within
                    armDir := 0  // timeout

// ===== Plots =====
plot(adj, "Window Cum Delta", color=color.blue, linewidth=2)
plot(flipSignal, "Flip Signal", color=color.yellow, linewidth=1, display=display.data_window)
plot(perBarDelta, "Per Bar Delta", color=perBarDelta >= 0 ? color.green : color.red,
     style=plot.style_columns, linewidth=2)
hline(0, "Zero", color=color.gray, linestyle=hline.style_solid)

// ===== Alerts =====
alertcondition(flipSignal > 0, title="Delta Flip Up", message="Delta Flip Signal: UP")
alertcondition(flipSignal < 0, title="Delta Flip Down", message="Delta Flip Signal: DOWN")
alertcondition(flipSignal != 0, title="Delta Flip Any", message="Delta Flip Signal: {{plot(\"Flip Signal\")}}")