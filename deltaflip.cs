#region Using
using System;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Xml.Serialization;
using System.Windows.Media;
using NinjaTrader.Data;
using NinjaTrader.Gui.Chart;
using NinjaTrader.NinjaScript;
using NinjaTrader.NinjaScript.DrawingTools;
#endregion

namespace NinjaTrader.NinjaScript.Indicators
{
    public class DeltaFlip_differentlogic : Indicator
    {
        // ===== Core =====
        [NinjaScriptProperty, Display(Name="Window Size (bars)", Order=0, GroupName="Core")]
        public int WindowSize { get; set; } = 20;

        [NinjaScriptProperty, Display(Name="SD Lookback (bars)", Order=1, GroupName="Core")]
        public int SdLookback { get; set; } = 100;

        // ===== Gate =====
        [NinjaScriptProperty, Display(Name="Threshold (× SD basis)", Order=0, GroupName="Gate")]
        public double ThresholdSd { get; set; } = 0.03;

        [NinjaScriptProperty, Display(Name="Use Window SD Basis", Order=1, GroupName="Gate")]
        public bool UseWindowSDBasis { get; set; } = true;

        [NinjaScriptProperty, Display(Name="Use Prior-Bar SD", Order=2, GroupName="Gate")]
        public bool UsePriorStdForGate { get; set; } = true;

        [NinjaScriptProperty, Display(Name="Gate SD Scale", Order=3, GroupName="Gate")]
        public double GateSdScale { get; set; } = 0.05;

        [NinjaScriptProperty, Display(Name="Min Abs (0=off)", Order=4, GroupName="Gate")]
        public double MinAbs { get; set; } = 0.0;

        // ===== Firing behaviour =====
        [NinjaScriptProperty, Display(Name="Fire Immediately On Pass", Order=0, GroupName="Fire")]
        public bool FireOnPass { get; set; } = true;

        [NinjaScriptProperty, Display(Name="Confirm Within Bars (0=unlimited)", Order=1, GroupName="Fire")]
        public int ConfirmWithinBars { get; set; } = 0;

        [NinjaScriptProperty, Display(Name="Min Flip Separation (bars)", Order=2, GroupName="Fire")]
        public int MinFlipSeparation { get; set; } = 0;

        [NinjaScriptProperty, Display(Name="Require Same-Side Hold", Order=3, GroupName="Fire")]
        public bool RequireSameSideHold { get; set; } = false;

        [NinjaScriptProperty, Display(Name="Hold Epsilon", Order=4, GroupName="Fire")]
        public double HoldEpsilon { get; set; } = 0.0;

        // ===== Visuals =====
        [NinjaScriptProperty, Display(Name="Draw Raw Zero-Cross Arrows", Order=0, GroupName="Visuals")]
        public bool DrawRawCrossArrows { get; set; } = true;

        [NinjaScriptProperty, Display(Name="Draw Gated Flip Arrows", Order=1, GroupName="Visuals")]
        public bool DrawGatedFlipArrows { get; set; } = true;

        [NinjaScriptProperty, Display(Name="Marker Radius (ticks)", Order=2, GroupName="Visuals")]
        public int MarkerRadiusTicks { get; set; } = 10;

        [NinjaScriptProperty, Display(Name="DEBUG: Paint dots", Order=0, GroupName="Debug")]
        public bool DebugPaintDots { get; set; } = true;

        // ===== Internals =====
        private Series<double> perBar, winSum, cumSnap;
        private SMA winMean;
        private StdDev winStd;
        private StdDev perBarStd;

        private double tapeCum = 0.0;
        private double lastTradePrice = double.NaN;
        private double lastTickPriceFallback = double.NaN;
        private int    lastFlipBar = int.MinValue;

        private int armDir = 0;    // +1 up, -1 down
        private int armBar = -1;
        private double armMaxMag = 0.0;

        protected override void OnStateChange()
        {
            if (State == State.SetDefaults)
            {
                Name = "DeltaFlip_differentlogic";
                Calculate = Calculate.OnEachTick;
                IsOverlay = true; 
                DrawOnPricePanel = true; 
                IsSuspendedWhileInactive = true;

                AddPlot(Brushes.DodgerBlue, "WindowCumDelta"); // Values[0]
                AddPlot(Brushes.Goldenrod, "FlipSignal");      // Values[1]
                AddPlot(Brushes.SlateGray, "PerBarDelta");     // Values[2]
                Plots[2].PlotStyle = PlotStyle.Bar; 
                Plots[2].Width = 2;
                AddLine(Brushes.DimGray, 0, "Zero");
            }
            else if (State == State.DataLoaded)
            {
                perBar  = new Series<double>(this);
                winSum  = new Series<double>(this);
                cumSnap = new Series<double>(this);

                winMean   = SMA(winSum, Math.Max(2, WindowSize));
                winStd    = StdDev(winSum, Math.Max(2, WindowSize));
                perBarStd = StdDev(perBar, Math.Max(2, SdLookback));
            }
        }

        protected override void OnMarketData(MarketDataEventArgs e)
        {
            if (e.MarketDataType != MarketDataType.Last) return;

            if (State == State.Historical)
            {
                bool anyReplay = false;
                try { for (int i = 0; i < BarsArray.Length; i++) if (BarsArray[i]?.IsTickReplay == true) { anyReplay = true; break; } }
                catch { anyReplay = (Bars?.IsTickReplay ?? false); }
                if (!anyReplay) return;
            }

            if (!double.IsNaN(lastTradePrice))
            {
                if      (e.Price > lastTradePrice) tapeCum += e.Volume;
                else if (e.Price < lastTradePrice) tapeCum -= e.Volume;
            }
            lastTradePrice = e.Price;
        }

        protected override void OnBarUpdate()
        {
            // Absolute earliest guard
            if (CurrentBar == 0)
            {
                cumSnap[0]   = tapeCum;
                perBar[0]    = 0;
                Values[0][0] = 0;
                Values[1][0] = 0;
                Values[2][0] = 0;
                return;
            }

            // Analyzer fallback: synthesize tape from 1-tick bars when no Tick Replay
            bool noReplayHistorical = (State == State.Historical) && !(Bars?.IsTickReplay ?? false);
            if (noReplayHistorical && BarsPeriod.BarsPeriodType == BarsPeriodType.Tick && BarsPeriod.Value == 1)
            {
                double p = Close[0];
                if (!double.IsNaN(lastTickPriceFallback))
                {
                    if      (p > lastTickPriceFallback) tapeCum += Volume[0];
                    else if (p < lastTickPriceFallback) tapeCum -= Volume[0];
                }
                lastTickPriceFallback = p;
            }

            // Per-bar delta from cumulative snapshot
            double prevCum  = cumSnap[1];
            double currCum  = tapeCum;
            cumSnap[0]      = currCum;
            perBar[0]       = currCum - prevCum;

            // Rolling window sum and detrend
            double sum = 0.0; 
            int back = Math.Min(WindowSize - 1, CurrentBar);
            for (int i = 0; i <= back; i++) sum += perBar[i];
            winSum[0] = sum;

            double adj = winSum[0] - winMean[0];
            Values[0][0] = adj;
            Values[2][0] = perBar[0];
            PlotBrushes[2][0] = perBar[0] >= 0 ? Brushes.MediumSeaGreen : Brushes.IndianRed;

            // Reset signal each bar
            Values[1][0] = 0;

            // Warm-up
            int warm = Math.Max(WindowSize, SdLookback) + 2;
            if (CurrentBar < warm) return;

            // Zero-cross detection
            double prevAdj = (winSum[1] - winMean[1]);
            bool crossUp   = (prevAdj <= 0 && adj >= 0);
            bool crossDown = (prevAdj >= 0 && adj <= 0);

            if (DrawRawCrossArrows && (crossUp || crossDown))
            {
                string rawTag = (crossUp ? "RAW_UP_" : "RAW_DN_") + CurrentBar;
                if (crossUp) Draw.ArrowUp(this, rawTag, false, 0, Low[0]  - Math.Max(1, MarkerRadiusTicks) * TickSize, Brushes.Gray);
                else         Draw.ArrowDown(this, rawTag, false, 0, High[0] + Math.Max(1, MarkerRadiusTicks) * TickSize, Brushes.Gray);
            }

            if (crossUp)   { armDir = +1; armBar = CurrentBar; armMaxMag = Math.Abs(adj); if (DebugPaintDots) Draw.Dot(this, "ARM_"+CurrentBar, false, 0, Close[0], Brushes.Gold); }
            if (crossDown) { armDir = -1; armBar = CurrentBar; armMaxMag = Math.Abs(adj); if (DebugPaintDots) Draw.Dot(this, "ARM_"+CurrentBar, false, 0, Close[0], Brushes.Gold); }

            if (RequireSameSideHold && armDir != 0)
            {
                if (armDir > 0 && adj < -HoldEpsilon) armDir = 0;
                if (armDir < 0 && adj >  HoldEpsilon) armDir = 0;
            }

            bool gatesOff = (ThresholdSd <= 0 && MinAbs <= 0);
            if (gatesOff && (crossUp || crossDown))
            {
                Fire(crossUp ? +1 : -1);
                return;
            }

            if (armDir != 0) armMaxMag = Math.Max(armMaxMag, Math.Abs(adj));

            int idx = UsePriorStdForGate ? 1 : 0;
            int safeIdx = Math.Min(CurrentBar, Math.Max(0, idx));

            double sdWinVal  = winStd[safeIdx];
            double sdPerVal  = perBarStd[safeIdx];
            if (double.IsNaN(sdWinVal)) sdWinVal = 0.0;
            if (double.IsNaN(sdPerVal)) sdPerVal = 0.0;

            double sdBasis = UseWindowSDBasis ? sdWinVal : sdPerVal;
            sdBasis = Math.Max(1e-6, sdBasis);

            double thr = ThresholdSd * sdBasis * Math.Max(1e-6, GateSdScale);

            bool pass = (ThresholdSd > 0 && armMaxMag >= thr) || (MinAbs > 0 && armMaxMag >= MinAbs);
            if (DebugPaintDots && armDir != 0 && pass)
                Draw.Dot(this, "PASS_"+CurrentBar, false, 0, Close[0], Brushes.Orange);

            if (armDir != 0 && pass)
            {
                if (FireOnPass)
                {
                    Fire(armDir);
                    armDir = 0;
                    return;
                }

                bool within = (ConfirmWithinBars <= 0) || (CurrentBar - armBar <= ConfirmWithinBars);
                bool sepOK  = (CurrentBar - lastFlipBar >= MinFlipSeparation);
                if (within && sepOK)
                {
                    Fire(armDir);
                    armDir = 0;
                }
                else if (!within)
                {
                    armDir = 0; // timeout
                }
            }
        }

        private void Fire(int colouredDir)
        {
            // ALWAYS publish the signal; only the drawing is optional
            Values[1][0] = colouredDir;
            lastFlipBar  = CurrentBar;

            if (DrawGatedFlipArrows)
            {
                string tag = (colouredDir > 0 ? "FLIP_UP_" : "FLIP_DN_") + CurrentBar;
                if (colouredDir > 0)
                    Draw.ArrowUp(this, tag, false, 0, Low[0]  - Math.Max(1, MarkerRadiusTicks) * TickSize, Brushes.LimeGreen);
                else
                    Draw.ArrowDown(this, tag, false, 0, High[0] + Math.Max(1, MarkerRadiusTicks) * TickSize, Brushes.Magenta);
            }
        }
    }
}

#region NinjaScript generated code. Neither change nor remove.

namespace NinjaTrader.NinjaScript.Indicators
{
	public partial class Indicator : NinjaTrader.Gui.NinjaScript.IndicatorRenderBase
	{
		private DeltaFlip_differentlogic[] cacheDeltaFlip_differentlogic;
		public DeltaFlip_differentlogic DeltaFlip_differentlogic(int windowSize, int sdLookback, double thresholdSd, bool useWindowSDBasis, bool usePriorStdForGate, double gateSdScale, double minAbs, bool fireOnPass, int confirmWithinBars, int minFlipSeparation, bool requireSameSideHold, double holdEpsilon, bool drawRawCrossArrows, bool drawGatedFlipArrows, int markerRadiusTicks, bool debugPaintDots)
		{
			return DeltaFlip_differentlogic(Input, windowSize, sdLookback, thresholdSd, useWindowSDBasis, usePriorStdForGate, gateSdScale, minAbs, fireOnPass, confirmWithinBars, minFlipSeparation, requireSameSideHold, holdEpsilon, drawRawCrossArrows, drawGatedFlipArrows, markerRadiusTicks, debugPaintDots);
		}

		public DeltaFlip_differentlogic DeltaFlip_differentlogic(ISeries<double> input, int windowSize, int sdLookback, double thresholdSd, bool useWindowSDBasis, bool usePriorStdForGate, double gateSdScale, double minAbs, bool fireOnPass, int confirmWithinBars, int minFlipSeparation, bool requireSameSideHold, double holdEpsilon, bool drawRawCrossArrows, bool drawGatedFlipArrows, int markerRadiusTicks, bool debugPaintDots)
		{
			if (cacheDeltaFlip_differentlogic != null)
				for (int idx = 0; idx < cacheDeltaFlip_differentlogic.Length; idx++)
					if (cacheDeltaFlip_differentlogic[idx] != null && cacheDeltaFlip_differentlogic[idx].WindowSize == windowSize && cacheDeltaFlip_differentlogic[idx].SdLookback == sdLookback && cacheDeltaFlip_differentlogic[idx].ThresholdSd == thresholdSd && cacheDeltaFlip_differentlogic[idx].UseWindowSDBasis == useWindowSDBasis && cacheDeltaFlip_differentlogic[idx].UsePriorStdForGate == usePriorStdForGate && cacheDeltaFlip_differentlogic[idx].GateSdScale == gateSdScale && cacheDeltaFlip_differentlogic[idx].MinAbs == minAbs && cacheDeltaFlip_differentlogic[idx].FireOnPass == fireOnPass && cacheDeltaFlip_differentlogic[idx].ConfirmWithinBars == confirmWithinBars && cacheDeltaFlip_differentlogic[idx].MinFlipSeparation == minFlipSeparation && cacheDeltaFlip_differentlogic[idx].RequireSameSideHold == requireSameSideHold && cacheDeltaFlip_differentlogic[idx].HoldEpsilon == holdEpsilon && cacheDeltaFlip_differentlogic[idx].DrawRawCrossArrows == drawRawCrossArrows && cacheDeltaFlip_differentlogic[idx].DrawGatedFlipArrows == drawGatedFlipArrows && cacheDeltaFlip_differentlogic[idx].MarkerRadiusTicks == markerRadiusTicks && cacheDeltaFlip_differentlogic[idx].DebugPaintDots == debugPaintDots && cacheDeltaFlip_differentlogic[idx].EqualsInput(input))
						return cacheDeltaFlip_differentlogic[idx];
			return CacheIndicator<DeltaFlip_differentlogic>(new DeltaFlip_differentlogic(){ WindowSize = windowSize, SdLookback = sdLookback, ThresholdSd = thresholdSd, UseWindowSDBasis = useWindowSDBasis, UsePriorStdForGate = usePriorStdForGate, GateSdScale = gateSdScale, MinAbs = minAbs, FireOnPass = fireOnPass, ConfirmWithinBars = confirmWithinBars, MinFlipSeparation = minFlipSeparation, RequireSameSideHold = requireSameSideHold, HoldEpsilon = holdEpsilon, DrawRawCrossArrows = drawRawCrossArrows, DrawGatedFlipArrows = drawGatedFlipArrows, MarkerRadiusTicks = markerRadiusTicks, DebugPaintDots = debugPaintDots }, input, ref cacheDeltaFlip_differentlogic);
		}
	}
}

namespace NinjaTrader.NinjaScript.MarketAnalyzerColumns
{
	public partial class MarketAnalyzerColumn : MarketAnalyzerColumnBase
	{
		public Indicators.DeltaFlip_differentlogic DeltaFlip_differentlogic(int windowSize, int sdLookback, double thresholdSd, bool useWindowSDBasis, bool usePriorStdForGate, double gateSdScale, double minAbs, bool fireOnPass, int confirmWithinBars, int minFlipSeparation, bool requireSameSideHold, double holdEpsilon, bool drawRawCrossArrows, bool drawGatedFlipArrows, int markerRadiusTicks, bool debugPaintDots)
		{
			return indicator.DeltaFlip_differentlogic(Input, windowSize, sdLookback, thresholdSd, useWindowSDBasis, usePriorStdForGate, gateSdScale, minAbs, fireOnPass, confirmWithinBars, minFlipSeparation, requireSameSideHold, holdEpsilon, drawRawCrossArrows, drawGatedFlipArrows, markerRadiusTicks, debugPaintDots);
		}

		public Indicators.DeltaFlip_differentlogic DeltaFlip_differentlogic(ISeries<double> input , int windowSize, int sdLookback, double thresholdSd, bool useWindowSDBasis, bool usePriorStdForGate, double gateSdScale, double minAbs, bool fireOnPass, int confirmWithinBars, int minFlipSeparation, bool requireSameSideHold, double holdEpsilon, bool drawRawCrossArrows, bool drawGatedFlipArrows, int markerRadiusTicks, bool debugPaintDots)
		{
			return indicator.DeltaFlip_differentlogic(input, windowSize, sdLookback, thresholdSd, useWindowSDBasis, usePriorStdForGate, gateSdScale, minAbs, fireOnPass, confirmWithinBars, minFlipSeparation, requireSameSideHold, holdEpsilon, drawRawCrossArrows, drawGatedFlipArrows, markerRadiusTicks, debugPaintDots);
		}
	}
}

namespace NinjaTrader.NinjaScript.Strategies
{
	public partial class Strategy : NinjaTrader.Gui.NinjaScript.StrategyRenderBase
	{
		public Indicators.DeltaFlip_differentlogic DeltaFlip_differentlogic(int windowSize, int sdLookback, double thresholdSd, bool useWindowSDBasis, bool usePriorStdForGate, double gateSdScale, double minAbs, bool fireOnPass, int confirmWithinBars, int minFlipSeparation, bool requireSameSideHold, double holdEpsilon, bool drawRawCrossArrows, bool drawGatedFlipArrows, int markerRadiusTicks, bool debugPaintDots)
		{
			return indicator.DeltaFlip_differentlogic(Input, windowSize, sdLookback, thresholdSd, useWindowSDBasis, usePriorStdForGate, gateSdScale, minAbs, fireOnPass, confirmWithinBars, minFlipSeparation, requireSameSideHold, holdEpsilon, drawRawCrossArrows, drawGatedFlipArrows, markerRadiusTicks, debugPaintDots);
		}

		public Indicators.DeltaFlip_differentlogic DeltaFlip_differentlogic(ISeries<double> input , int windowSize, int sdLookback, double thresholdSd, bool useWindowSDBasis, bool usePriorStdForGate, double gateSdScale, double minAbs, bool fireOnPass, int confirmWithinBars, int minFlipSeparation, bool requireSameSideHold, double holdEpsilon, bool drawRawCrossArrows, bool drawGatedFlipArrows, int markerRadiusTicks, bool debugPaintDots)
		{
			return indicator.DeltaFlip_differentlogic(input, windowSize, sdLookback, thresholdSd, useWindowSDBasis, usePriorStdForGate, gateSdScale, minAbs, fireOnPass, confirmWithinBars, minFlipSeparation, requireSameSideHold, holdEpsilon, drawRawCrossArrows, drawGatedFlipArrows, markerRadiusTicks, debugPaintDots);
		}
	}
}

#endregion
